package io.wyden.orderhistory.service;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.orderhistory.infrastructure.rabbit.SimplifiedTradingMessageConsumer;
import io.wyden.orderhistory.infrastructure.rabbit.TradingMessageConsumer;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true"})
public abstract class AsyncOrderStateProcessorIntegrationTestBase extends TradingMessageConsumerIntegrationTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncOrderStateProcessorIntegrationTestBase.class);

    @Autowired
    AsyncOrderStateProcessor asyncOrderStateProcessor;

    @MockBean
    TradingMessageConsumer tradingMessageConsumer;

    @Autowired
    protected RabbitExchange<OrderState> exchange;

    private final BlockingQueue<OrderState> consumerOrderStateQueue = new LinkedBlockingQueue<>();
    private String orderStateTag;

    @BeforeEach
    public void setUp() {
        consumer = new SimplifiedTradingMessageConsumer(
            rabbitIntegrator,
            rabbitDestinations.tradingIngressExchange(rabbitIntegrator),
            orderEventRepository,
            asyncOrderStateProcessor,
            telemetry.getTracing(),
            queueName,
            "async-order-state-processor-test-consumer");

        RabbitQueue<OrderState> orderStateQueue = new RabbitQueueBuilder<OrderState>(rabbitIntegrator).setQueueName("temporary-order-state-queue").declare();
        orderStateQueue.bindWithHeaders(exchange, MatchingCondition.ALL, Map.of());
        orderStateTag = orderStateQueue.attachConsumer(OrderState.parser(), (orderState, params) -> {
            LOGGER.info("Received order state: {}", orderState);
            consumerOrderStateQueue.add(orderState);
            return consumed();
        });
    }

    @AfterEach
    void tearDownBase() throws IOException {
        rabbitIntegrator.getConsumptionChannel().basicCancel(orderStateTag);
    }

    OrderState awaitOrderState() {
        return await().atMost(Duration.ofSeconds(5)).until(consumerOrderStateQueue::poll, Objects::nonNull);
    }
}
