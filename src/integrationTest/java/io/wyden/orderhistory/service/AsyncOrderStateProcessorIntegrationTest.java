package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.Test;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

public class AsyncOrderStateProcessorIntegrationTest extends AsyncOrderStateProcessorIntegrationTestBase {

    @Test
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void shouldProcessValidOemsResponseAndSaveEvent() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());

        // Create a test OemsResponse
        OemsResponse response = createTestOemsResponse(UUID.randomUUID().toString());

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();

        await().atMost(3, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        await().atMost(10, TimeUnit.SECONDS).untilAsserted(() -> {
            OrderState orderState = awaitOrderState();
            assertThat(orderState).isNotNull();
            assertThat(orderState.getOrderId()).isEqualTo(response.getOrderId());
        });
    }
}
